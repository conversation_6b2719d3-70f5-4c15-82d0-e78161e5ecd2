using UnityEngine;

internal class TargetBallStateStandoff: ITargetBallState
{
    private readonly TargetBall _owner;
    
    public float ballSpeedMetersPerSecond => 0;

    internal TargetBallStateStandoff(TargetBall owner)
    {
        _owner = owner;
    }
    
    public Vector3 Update()
    {
        return _owner.transform.position;
    }

    public void FixedUpdate()
    {
        if(_owner.isIntersectingBeam)
            _owner.SetRandomSphereState(false, null);
    }
    
    public void NearCollisionWith(TargetBall other)
    {
        _owner.SetRandomSphereState(false, null);
    }
}