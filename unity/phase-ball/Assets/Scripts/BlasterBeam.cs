using System.Collections.Generic;
using UnityEngine;

public class BlasterBeam : MonoBehaviour
{
    [Header("References")]
    [Tooltip("The controller transform (usually the XR Controller object).")]
    public Transform controller;

    [Header("<PERSON>am Settings")]
    [Tooltip("Maximum beam length in meters.")]
    public float maxBeamLength = 100f;

    [Tooltip("Those game objects that the beam cannot pass through.")]
    public LayerMask collisionMask;

    private readonly List<TargetBall> _targetBalls = new();
    
    private MeshRenderer _beamRenderer;

    private bool _isEngaged;
    public bool isEngaged => _isEngaged;

    void Start()
    {
        _beamRenderer = GetComponent<MeshRenderer>();
        _beamRenderer.enabled = false; // only turn on when beam is engaged
        
        // lazer beams are not like physical objects, shouldn't have shadows or cast shadows
        _beamRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
        _beamRenderer.receiveShadows = false;
        
        _targetBalls.AddRange(FindObjectsByType<TargetBall>(FindObjectsSortMode.None));
    }
    
    public void LateUpdate()
    {
        // we use late update to ensure the controller's position has been finalized/set by `TrackedPoseDriver`'s
        // `Update()` before we render the beam's location here.
        
        if(_isEngaged)
            PlaceAndSizeBeam();
        return;
        
        void PlaceAndSizeBeam()
        {
            var hit = FindCurrentlyHitTargetBall();
            float beamLengthMeters = hit.HasValue ? hit.Value.hit.distance : maxBeamLength;
            
            // Compute scale for Unity Cylinder (height = 2 * scale.y)
            float beamLocalScaleY = beamLengthMeters / 2f;
            transform.localScale = new Vector3(transform.localScale.x, beamLocalScaleY, transform.localScale.z);

            // Position beam so its near end sits at controller
            transform.localPosition = new Vector3(0f, beamLengthMeters/2f, 0f);

            // Align beam forward with controller forward
            transform.localRotation = Quaternion.identity;
        }
    }
    
    public void FixedUpdate()
    {
        if (_isEngaged)
        {
            var hit = FindCurrentlyHitTargetBall();
        
            foreach(TargetBall ball in _targetBalls)
                if(hit != null && hit.Value.ball != ball)
                    ball.intersectingBeamDirection = null;
        
            if(hit != null)
                hit.Value.ball.intersectingBeamDirection = controller.forward.normalized;
        }
        else
        {
            foreach(TargetBall ball in _targetBalls)
                    ball.intersectingBeamDirection = null;
        }
    }
    
    private (RaycastHit hit, TargetBall ball)? FindCurrentlyHitTargetBall()
    {
        if (Physics.Raycast(controller.position, controller.forward, out RaycastHit hit, maxBeamLength, collisionMask))
        {
            hit.collider.TryGetComponent(out TargetBall hitBall);
            if (hitBall != null)
                return (hit,  hitBall);
        }
        
        return null;
    }


    internal void Engage() // expect to be called every frame, so be cheap
    {
        if (_isEngaged)
            return;

        _beamRenderer.enabled = true;
        _isEngaged = true;
    }

    internal void Disengage()  // expect to be called every frame, so be cheap
    {
        if (!_isEngaged)
            return;

        _beamRenderer.enabled = false;
        _isEngaged = false;
    }
}
