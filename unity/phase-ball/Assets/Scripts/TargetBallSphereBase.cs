using System;
using UnityEngine;

internal abstract class TargetBallSphereBase : ITargetBallState
{
    /**
     * When the beam is hitting the ball, how fast the ball moves in the direction of beam's push.
     */
    private const float BeamPushSpeedUnityUnitsPerSecond = 0.5f;

    protected readonly TargetBall Owner;

    public float ballSpeedMetersPerSecond { get; }

    /**
     * How long the ball should be in sphere mode without being hit before considering changing into standoff mode.
     * Could be longer if the timer expires while in the transit cone.
     */
    private readonly TimeSpan _standoffTimerDuration = TimeSpan.FromSeconds(10);

    /**
     * The last time `ResetStandoffStartMoment()` was invoked.
     */
    private double _lastStandoffStartMomentRestUnityTime;

    /**
     * When the ball should transition into standoff mode if not hit by the user or in the transit cone.
     */
    private double _nextStandoffStartMomentUnityTime;

    /**
     * The target ball's renderer.
     */
    private readonly Renderer _renderer;
    
    private Vector3 _unappliedCumulativeBeamForce = new(0, 0, 0);

    protected TargetBallSphereBase(TargetBall owner, float ballSpeedMetersPerSecond)
    {
        Owner = owner;
        this.ballSpeedMetersPerSecond = ballSpeedMetersPerSecond;
        _renderer = Owner.GetComponent<Renderer>();
        ResetStandoffStartMoment();
    }

    public Vector3 Update()
    {
        Recolor();
        Vector3 proposedPosition = ProposeNewPosition() + _unappliedCumulativeBeamForce;
        _unappliedCumulativeBeamForce = new(0, 0, 0);

        return proposedPosition;
    }
    
    protected abstract Vector3 ProposeNewPosition();
    
    /**
     * If this ball's movement is the same as `other` (disregarding speed), mutate this ball in some way to be different.
     */
    public abstract void EnsureDifferentMovementThan(TargetBallSphereBase other);
    
    public void NearCollisionWith(TargetBall other)
    {
        Owner.SetRandomSphereState(true, this);
    }

    public void FixedUpdate()
    {
        Vector3? intersectingBeamDirection = Owner.intersectingBeamDirection;
        
        if (intersectingBeamDirection != null) // if beam is hitting ball
        {
            ResetStandoffStartMoment();
            _unappliedCumulativeBeamForce+= ComputeBeamForce(intersectingBeamDirection.Value);
        }

        if (Time.timeAsDouble >= _nextStandoffStartMomentUnityTime &&
            !Owner.IsInTransitOnlyConeZone()) // don't halt the ball's movement inside the transit only zone
        {
            Owner.SetState(new TargetBallStateStandoff(Owner));
        }
    }

    private Vector3 ComputeBeamForce(Vector3 intersectingBeamDirection)
    {
        float expansionDistanceUnityUnits = Time.fixedDeltaTime * BeamPushSpeedUnityUnitsPerSecond;
        Vector3 movementForceVector = intersectingBeamDirection * expansionDistanceUnityUnits;
        return movementForceVector;
    }

    private void ResetStandoffStartMoment()
    {
        _lastStandoffStartMomentRestUnityTime = Time.timeAsDouble;
        _nextStandoffStartMomentUnityTime =
            _lastStandoffStartMomentRestUnityTime + _standoffTimerDuration.TotalSeconds;
    }

    /**
     * Update the ball's color on a green...red gradient based on how soon the standoff timer expires.
     */
    private void Recolor()
    {
        float percentTimeTowardsStandoff = (float)
            ((Time.timeAsDouble - _lastStandoffStartMomentRestUnityTime) / _standoffTimerDuration.TotalSeconds);
        percentTimeTowardsStandoff = Math.Clamp(percentTimeTowardsStandoff, 0, 1); // could be 1+ if in transit cone
        _renderer.material.color = new Color(percentTimeTowardsStandoff, 1.0f - percentTimeTowardsStandoff, 0);
    }

}