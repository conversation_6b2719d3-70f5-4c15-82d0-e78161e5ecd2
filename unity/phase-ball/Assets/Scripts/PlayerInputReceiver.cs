using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit.Inputs.Readers;

public class PlayerInputReceiver : MonoBehaviour
{
    public XRInputValueReader<float> rightTrigger;  // value set in Unity editor

    public Blaster blaster;
    
    void Update()
    {
        /*
         * We sample for input and invoke `Engage/Disengage` every frame because the engagement state of the beam
         * determines if it is drawn, and we want the beam's transition from drawn/undrawn and/or movement to be
         * visually responsive.
         */
        if (rightTrigger != null)
        {
            float rightTriggerValue = rightTrigger.ReadValue(); // will be on [0...1]
            blaster.isTriggered = rightTriggerValue > 0;
        }
    }
}
