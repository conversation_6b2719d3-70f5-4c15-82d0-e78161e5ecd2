using UnityEngine;

public class Blaster : MonoBehaviour
{
    /**
     * At maximum charge, how long the blaster could emit a beam before depleting its energy.
     */
    private const float MaxEnergyLevelInSeconds = 3.0f;
    
    /**
     * How long after the player had released the trigger before the blaster will start to recharge.
     */
    private const float RechargeCooldownSeconds = 0.3f;
    
    public GameObject beamGameObject; // populated from unity

    /**
     * Script instance associated with the blaster beam.
     */
    private BlasterBeam _blasterBeam;

    /**
     * Whether the trigger is depressed to any degree.
     */
    private bool _isTriggered;

    internal bool isTriggered
    {
        get => _isTriggered;

        set
        {
            bool transitioningToNotTriggered = value == false && _isTriggered;
            _isTriggered = value; 
            if(transitioningToNotTriggered) // because may get called with consecutive same state
                _rechargingAllowedStartMomentUnityTime = Time.time + RechargeCooldownSeconds;
        }
    }
    
    private float _energyLevelInSeconds = MaxEnergyLevelInSeconds;

    /**
     * The moment in time at which recharging of the blaster may start.
     */
    private float _rechargingAllowedStartMomentUnityTime;
    
    void Start()
    {
        _blasterBeam = beamGameObject.GetComponent<BlasterBeam>();
    }

    void FixedUpdate()
    {
        if (isTriggered)
        {
            if (_energyLevelInSeconds > 0)
            {
                if(!_blasterBeam.isEngaged)
                    _blasterBeam.Engage();
                _energyLevelInSeconds -= Time.fixedDeltaTime;
            }
            else
            {
                if(_blasterBeam.isEngaged)
                    _blasterBeam.Disengage();
            }
        }
        else
        {
            if(_blasterBeam.isEngaged)
                _blasterBeam.Disengage();
        }
        
        if(Time.time > _rechargingAllowedStartMomentUnityTime && !isTriggered)
            _energyLevelInSeconds = Mathf.Clamp(_energyLevelInSeconds+Time.fixedDeltaTime, 0f, MaxEnergyLevelInSeconds);
    }
}
