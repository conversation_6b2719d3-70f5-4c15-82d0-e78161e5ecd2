using System;
using UnityEngine;

internal class TargetBallStateActivating: ITargetBallState
{
    private readonly float _yPosAtConstruction;
    
    private readonly TargetBall _owner;

    private readonly GameObject _playerCamera;

    private float? _firstUpdateMomentUnitySeconds;
    
    public float ballSpeedMetersPerSecond => 0.7f;
    
    internal TargetBallStateActivating(TargetBall owner, GameObject playerCamera)
    {
        _owner = owner;
        _playerCamera = playerCamera;
        _yPosAtConstruction = _owner.transform.position.y;
    }
    
    
    public Vector3 Update()
    {
        _firstUpdateMomentUnitySeconds ??= Time.time;
        
        float elapsedActivatingTimeSeconds = Time.time - _firstUpdateMomentUnitySeconds.Value;
        float proposedBallPosY = _yPosAtConstruction + elapsedActivatingTimeSeconds * ballSpeedMetersPerSecond;
        float newBallPosY = Math.Min(proposedBallPosY, _playerCamera.transform.position.y);
        return new Vector3(_owner.transform.position.x, newBallPosY, _owner.transform.position.z);
    }

    public void FixedUpdate()
    {
        if(_owner.transform.position.y >= _playerCamera.transform.position.y)
            _owner.SetRandomSphereState(false, null);
    }

    public void NearCollisionWith(TargetBall other)
    {
        // do nothing
    }
}