using System;
using System.Collections.Generic;
using UnityEngine;

public class TargetBall : MonoBehaviour
{
    private const float DefaultUnitySphereRadiusUnityUnits = 0.5f;

    /**
     * The amount of up head tilt above the horizon that should be comfortable for a player.
     */
    private const float TransitOnlyEyeLevelAngleRadians = Mathf.Deg2Rad * 40;
    
    /**
     * The min distance along the curve of the movement sphere this target ball should move per second (before any
     * potential adjustments are made by subclasses). Serves as a sort of master tempo factor for all balls.
     */
    private const float MinSphereBallSpeedMetersPerSecond = .5f;

    /**
     * The max distance along the curve of the movement sphere this target ball should move per second (before any
     * potential adjustments are made by subclasses). Serves as a sort of master tempo factor for all balls.
     */
    private const float MaxSphereBallSpeedMetersPerSecond = 6;

    private float _targetBallRadius;

    private float _minimumAltitude;

    private float _targetBallDiameterSquared;
    
    private ITargetBallState _state;

    public GameObject playerCamera;

    private Vector3? _intersectingBeamDirection;

    private readonly List<TargetBall> _otherTargetBalls = new();
    
    internal Vector3? intersectingBeamDirection
    {
        get => _intersectingBeamDirection;

        set => _intersectingBeamDirection = value?.normalized;
    }
    
    internal bool isIntersectingBeam => intersectingBeamDirection != null;

    void Start()
    {
        _state = new TargetBallStateInactive(this, playerCamera);
        _targetBallRadius = DefaultUnitySphereRadiusUnityUnits * 
                           Mathf.Max(transform.lossyScale.x, transform.lossyScale.y, transform.lossyScale.z);
        _minimumAltitude = _targetBallRadius;
        float targetBallDiameter = _targetBallRadius + _targetBallRadius;
        _targetBallDiameterSquared = targetBallDiameter * targetBallDiameter; 

        foreach (TargetBall ball in FindObjectsByType<TargetBall>(FindObjectsSortMode.None))
        {
            if(ball != this)
                _otherTargetBalls.Add(ball);
        }
    }
    
    public void Update()
    {
        Vector3 proposedPosition = _state.Update();
        ChangePositionOrState(proposedPosition);
    }
    
    public void FixedUpdate()
    {
        _state.FixedUpdate();
    }

    private void ChangePositionOrState(Vector3 proposedPosition)
    {
        if(WouldBeAtOrBelowMinAltitude(proposedPosition))
            proposedPosition = new Vector3(proposedPosition.x, _minimumAltitude, proposedPosition.z);
        
        foreach (TargetBall other in _otherTargetBalls)
        {
            // `.sqrMagnitude` and not `.magnitude` to avoid a sqrt() operation on an `Update()` call for performance
            float distanceBetweenBallsSquared = (proposedPosition - other.transform.position).sqrMagnitude;
            bool wouldIntersect = distanceBetweenBallsSquared <= _targetBallDiameterSquared;
            
            // if proposed position would cause the two balls to touch
            if (wouldIntersect)
            {
                other._state.NearCollisionWith(this); // call first so other can see the state of this ball before 
                _state.NearCollisionWith(this);       // a potential change 
               return;
            }
        }

        gameObject.transform.position = proposedPosition;
    }
    internal void SetState(ITargetBallState state)
    {
        _state = state;
    }

    /// <summary>
    /// Changes the ball's state to a random sphere movement state.
    /// </summary>
    /// <param name="preserveBallSpeed">Whether to preserve the ball's current speed after the change of state.</param>
    /// <param name="movementUnlike">If non-null, ensure the new state's movement is not identical to
    /// <code>movementUnlike</code></param>
    internal void SetRandomSphereState(bool preserveBallSpeed, TargetBallSphereBase movementUnlike)
    {
        float ballSpeedMetersPerSecond = preserveBallSpeed
            ? _state.ballSpeedMetersPerSecond
            : MakeRandomSphereBallSpeedMetersPerSecond();
        
        List<TargetBallSphereBase> possibleNextStates = new List<TargetBallSphereBase>
            {
                new TargetBallStateSpherePolar(this, ballSpeedMetersPerSecond),
                new TargetBallFreeBounce(this, ballSpeedMetersPerSecond)
            };
        if(!IsInTransitOnlyConeZone()) // Azimuth would orbit inside the zone, so exclude
            possibleNextStates.Add(new TargetBallStateSphereAzimuth(this, ballSpeedMetersPerSecond));

        TargetBallSphereBase proposedState = possibleNextStates[Randomness.Next(possibleNextStates.Count)];
        if (movementUnlike != null)
            proposedState.EnsureDifferentMovementThan(movementUnlike);

        _state = proposedState;
    }

    internal bool IsInTransitOnlyConeZone()
    {
        /*
         * Imagine a cone (like a V shape) starting at x=0,z=0,y=(player's head height) and extending up all the way to
         * the top of the court. The angle of the cone inside the V is controlled by an external "gaze" angle measured
         * from the horizon up to a maximum gaze line which is the perimeter of the V. We set the gaze angle via
         * `TransitOnlyEyeLevelAngleRadians` to be a "comfortable" amount of upward head tilt for the user.  This in
         * essence means that all the points inside the cone are "uncomfortable" to look at. We don't want the player
         * to feel they have to continually visually scan inside the "uncomfortably" cone, so our goal is to never let
         * a ball stay in the cone indefinitely (e.g. standoff inside the cone or TargetBallStateSphereAzimuth inside
         * the cone). This method tests whether the ball is currently inside the cone. Balls should only ever transit
         * through this zone, not stay in the zone forever.
         */
        
        float ballHeightOverPlayerHeadMeters = transform.position.y - playerCamera.transform.position.y;
        if(ballHeightOverPlayerHeadMeters <= 0) // means we have to look down to see ball, not up
            return false;
        
        // the angle measured from the +Y vector to TransitOnlyEyeLevelAngleRadians
        float coneHalfAngleRadians =  MathF.PI / 2f - TransitOnlyEyeLevelAngleRadians;
        
        // the circle slice of the cone at the ball's height
        float radiusOfTransitZoneCircle = MathF.Tan(coneHalfAngleRadians) * ballHeightOverPlayerHeadMeters;
        
        // square distance comparison to avoid expensive sqrt function 
        float radiusOfTransitZoneCircleSquared = radiusOfTransitZoneCircle * radiusOfTransitZoneCircle;
        float ballDistanceToCircleCenterSquared = 
            transform.position.x * transform.position.x + transform.position.z * transform.position.z;

        return ballDistanceToCircleCenterSquared <= radiusOfTransitZoneCircleSquared;
    }
    
    internal SphericalCoordinate GetSphericalPosition()
    {
        // from
        // https://en.wikipedia.org/w/index.php?title=Spherical_coordinate_system&oldid=1303772995#Cartesian_coordinates
        // except in wikipedia's notation the Z axis points up and that's Unity's Y asis so the formulas are slightly
        // adjusted
        
        Vector3 pos = transform.position;

        // Radius (distance from origin)
        float r = pos.magnitude;
        
        // Inclination (angle down from +Y axis)
        float theta = Mathf.Acos(pos.y / r);

        // Azimuth (angle around Y axis, like polar coords)
        float phi = 0;
        if (pos.x > 0)
            phi = Mathf.Atan(pos.z / pos.x);
        else if(pos.x < 0 && pos.z >= 0)
            phi = Mathf.Atan(pos.z / pos.x) + Mathf.PI;
        else if(pos.x < 0 && pos.z < 0)
            phi = Mathf.Atan(pos.z / pos.x) - Mathf.PI;
        else if (pos.x == 0 && pos.z > 0)
            phi = Mathf.PI / 2;
        else if (pos.x == 0 && pos.z < 0)
            phi = Mathf.PI / -2;
        
        while(phi < 0)
            phi += 2 * Mathf.PI;
        
        return new SphericalCoordinate(r, new PolarAngle(theta), new AzimuthAngle(phi));
    }

    internal bool WouldBeAtOrBelowMinAltitude(Vector3 position)
    {
        return position.y < _minimumAltitude;
    }
    
    private float MakeRandomSphereBallSpeedMetersPerSecond()
    {
        return MinSphereBallSpeedMetersPerSecond + 
               (MaxSphereBallSpeedMetersPerSecond - MinSphereBallSpeedMetersPerSecond) * (float)Randomness.NextDouble();
    }
}
