using System;
using UnityEngine;

internal class TargetBallStateSpherePolar: TargetBallSphereBase
{
    private bool _moveTowardsYAxis;

    internal TargetBallStateSpherePolar(TargetBall owner, float ballSpeedMetersPerSecond) 
        : base(owner, ballSpeedMetersPerSecond)
    {
        _moveTowardsYAxis = Randomness.Next(2) == 0;
    }
    
    protected override Vector3 ProposeNewPosition()
    {
        var currentPosition = Owner.GetSphericalPosition();
        
        // since the polar target ball is always moving along a great circle that is a "slice" of the movement bounding
        // sphere, the radius of the sphere matches the radius of the circle the target ball is traveling along.
        //
        // recall that the arc length L of an arc on that great circle is determined by:
        //
        //    L = r * theta
        //
        // for L = 1 meter:
        //
        //    1 = r * theta
        //    1/r = theta
        //
        // so:
        float numRadiansForOneMeterOfArcLength = 1f / currentPosition.Radius;
        float ballSpeedRadiansPerSecond = ballSpeedMetersPerSecond * numRadiansForOneMeterOfArcLength;
        
        float newTheta = currentPosition.PolarAngle.ToRadians() + Time.deltaTime * ballSpeedRadiansPerSecond * 
                         (_moveTowardsYAxis? -1 : 1);
        
        float newPhi = currentPosition.AzimuthAngle.ToRadians();
        if (newTheta < 0)
        {
            newTheta *= -1;
            newPhi += MathF.PI;
            newPhi %= 2 * MathF.PI;
            _moveTowardsYAxis = false;
        }
        
        SphericalCoordinate proposedPositionSphere = 
            new SphericalCoordinate(currentPosition.Radius, new PolarAngle(newTheta), new AzimuthAngle(newPhi));
        Vector3 proposedPosition = proposedPositionSphere.ToCartesianCoordinate();

        if (Owner.WouldBeAtOrBelowMinAltitude(proposedPosition))
            _moveTowardsYAxis = true;

        return proposedPosition;
    }
    
    public override void EnsureDifferentMovementThan(TargetBallSphereBase other)
    {
        TargetBallStateSpherePolar otherAsMyType = other as TargetBallStateSpherePolar;
        if (otherAsMyType == null)
            return;

        _moveTowardsYAxis = !otherAsMyType._moveTowardsYAxis;
    }
}