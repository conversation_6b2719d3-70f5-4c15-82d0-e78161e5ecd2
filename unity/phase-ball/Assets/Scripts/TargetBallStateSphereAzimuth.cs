using System;
using UnityEngine;

internal class TargetBallStateSphereAzimuth: TargetBallSphereBase
{
    private bool _moveClockWise;
    
    internal TargetBallStateSphereAzimuth(TargetBall owner, float ballSpeedMetersPerSecond) 
        : base(owner, ballSpeedMetersPerSecond)
    {
        _moveClockWise = Randomness.Next(2) == 0;
    }
    
    protected override Vector3 ProposeNewPosition()
    {
        SphericalCoordinate currentPosition = Owner.GetSphericalPosition();
        
        // this formula is a mistake because it was copied from `TargetBallStateSpherePolar` thinking that the 
        // radius of the 2d circle this target ball moves around is the same radius as the movement sphere and
        // that is not true. however, it worked out in practice because as the 2d transit circle moves up and away
        // from the center of the sphere the ball slows down instead of speeding up, which is nice.
        float azimuthSpeedRadiansPerSecond = ballSpeedMetersPerSecond / currentPosition.Radius;
        
        float phi = currentPosition.AzimuthAngle.ToRadians() +
                 Time.deltaTime * azimuthSpeedRadiansPerSecond * (_moveClockWise ? -1 : 1);
        
        while(phi > 2 * MathF.PI)
            phi -= 2 * MathF.PI;
        
        while(phi < 0)
            phi += 2 * MathF.PI;

        SphericalCoordinate pos = new(currentPosition.Radius, currentPosition.PolarAngle, new AzimuthAngle(phi));
        return pos.ToCartesianCoordinate();
    }

    public override void EnsureDifferentMovementThan(TargetBallSphereBase other)
    {
        TargetBallStateSphereAzimuth otherAsMyType = other as TargetBallStateSphereAzimuth;
        if (otherAsMyType == null)
            return;

        _moveClockWise = !otherAsMyType._moveClockWise;
    }
}