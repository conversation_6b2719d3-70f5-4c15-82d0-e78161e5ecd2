using System;
using UnityEngine;

internal readonly struct SphericalCoordinate
{
    public readonly float Radius;

    public readonly AzimuthAngle AzimuthAngle;
    
    public readonly PolarAngle PolarAngle;

    internal SphericalCoordinate(float radius, PolarAngle theta, AzimuthAngle phi)
    {
        if(radius < 0)
            throw new ArgumentOutOfRangeException(nameof(radius));
        
        
        Radius = radius;
        PolarAngle = theta;
        AzimuthAngle = phi;
    }

    internal Vector3 ToCartesianCoordinate()
    {
        float x = Radius * Mathf.Sin(PolarAngle.ToRadians()) * Mathf.Cos(AzimuthAngle.ToRadians());
        float y = Radius * Mathf.Cos(PolarAngle.ToRadians());
        float z = Radius * Mathf.Sin(PolarAngle.ToRadians()) * Mathf.Sin(AzimuthAngle.ToRadians());
        
        return new Vector3(x, y, z);
    }
}