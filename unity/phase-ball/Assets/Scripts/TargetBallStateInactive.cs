using UnityEngine;

internal class TargetBallStateInactive: ITargetBallState
{
    private readonly TargetBall _owner;
    
    private readonly GameObject _playerCamera;

    internal TargetBallStateInactive(TargetBall owner, GameObject playerCamera)
    {
        _owner = owner;
        _playerCamera = playerCamera;
    }
    
    public void FixedUpdate()
    {
        if (_owner.isIntersectingBeam)
            _owner.SetState(new TargetBallStateActivating(_owner, _playerCamera));
    }

    public float ballSpeedMetersPerSecond => 0;

    public Vector3 Update()
    {
        return _owner.transform.position;
    }
    
    public void NearCollisionWith(TargetBall other)
    {
        // do nothing
    }
}